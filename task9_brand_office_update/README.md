# Task 9: Brand Office Address Update

Cập nhật cột title trong bảng brand_office dựa trên tọa độ và dữ liệu geometry từ task 4.

## Mục tiêu

Sử dụng thư viện geometry để xác định tọa độ của brand_office thuộc xã/phường nào, sau đó cập nhật lại cột title với format mới:
- **Giữ lại**: số nh<PERSON>, tên đư<PERSON> (khu dân cư), xã/phường, tỉnh/thành phố
- **Bỏ đi**: quận/huyện

## C<PERSON>u trúc thư mục

```
task9_brand_office_update/
├── README.md                           # File này
├── task9_brand_office_address_update.py # Script chính
├── exports/                            # Thư mục chứa kết quả
│   ├── task9_brand_office_update.log   # Log file
│   └── backup/                         # Backup dữ liệu trướ<PERSON> khi cập nhật
└── requirements.txt                    # Dependencies
```

## Dependencies

- `pymysql`: Kết nối MySQL database
- `shapely`: Xử lý geometry và point-in-polygon operations
- `json`: Parse dữ liệu geometry
- `logging`: Ghi log chi tiết

## Cách sử dụng

### 1. Cài đặt dependencies

```bash
pip install shapely pymysql
```

### 2. Chạy script

```bash
cd task9_brand_office_update
python task9_brand_office_address_update.py
```

### 3. Các bước thực hiện

1. **Kiểm tra schema** bảng brand_office
2. **Xem dữ liệu mẫu** từ brand_office và geo_ward
3. **Load ward geometries** từ bảng geo_ward
4. **Dry run** - kiểm tra kết quả trước khi cập nhật
5. **Xác nhận** từ user trước khi cập nhật thật
6. **Cập nhật database** nếu user đồng ý

## Cấu trúc dữ liệu

### Bảng brand_office
- `id`: Primary key
- `title`: Địa chỉ hiện tại (sẽ được cập nhật)
- `latitude`, `longitude`: Tọa độ GPS
- `updated`, `updated_by`: Thông tin cập nhật

### Bảng geo_ward
- `id`: Primary key
- `ward_title`: Tên xã/phường
- `province_title`: Tên tỉnh/thành phố
- `geometry`: Dữ liệu geometry (JSON format)

## Logic xử lý

### 1. Parse địa chỉ hiện tại
```
Input: "123 Nguyễn Văn A, Phường B, Quận C, TP.HCM"
Output: 
- street_number: "123 Nguyễn Văn A"
- street_name: "Phường B"
```

### 2. Point-in-polygon check
```python
point = Point(longitude, latitude)
for ward_geometry in ward_geometries:
    if ward_geometry.contains(point):
        return ward_info
```

### 3. Format địa chỉ mới
```
Output: "123 Nguyễn Văn A, Phường B, TP.HCM"
```

## Xử lý lỗi

- **Geometry parse error**: Skip ward đó và log warning
- **Point not found**: Log warning với tọa độ
- **Database error**: Rollback transaction
- **Connection error**: Retry hoặc exit

## Log format

```
2025-07-16 22:34:43,994 - INFO - Kết nối database thành công
2025-07-16 22:34:44,026 - INFO - Office ID 541: Cũ: "123 ABC, Phường X, Quận Y, TP.HCM" -> Mới: "123 ABC, Phường X, TP.HCM"
2025-07-16 22:34:44,195 - WARNING - Không tìm thấy ward cho Office ID 2 tại (21.03385, 105.81716)
```

## Backup và Recovery

Trước khi chạy script thật, nên backup bảng brand_office:

```sql
CREATE TABLE brand_office_backup_task9 AS SELECT * FROM brand_office;
```

## Hiện trạng

- ✅ Script đã được tạo và test cơ bản
- ⚠️ Cần xác định format dữ liệu geometry trong bảng geo_ward
- ⚠️ Cần test với dữ liệu thật

## Vấn đề cần giải quyết

1. **Format geometry**: Dữ liệu trong geo_ward có thể là GeoJSON hoặc ArcGIS format
2. **Performance**: 32,672 brand offices cần xử lý
3. **Data quality**: Một số tọa độ có thể không chính xác

## Liên hệ

- Task này là phần của dự án cập nhật hệ thống hành chính VN
- Liên quan đến task 4 (crawl geometry data) và task 7 (sync coordinates)
