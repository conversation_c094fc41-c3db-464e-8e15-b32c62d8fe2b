-- Task 9: Brand Office Address Update - Backup Script
-- Tạo backup tr<PERSON><PERSON><PERSON> khi thực hiện cập nhật

-- 1. Backup toàn bộ bảng brand_office
CREATE TABLE brand_office_backup_task9 AS 
SELECT * FROM brand_office;

-- 2. Tạo index cho backup table (optional, để query nhanh hơn)
ALTER TABLE brand_office_backup_task9 ADD INDEX idx_backup_id (id);

-- 3. <PERSON><PERSON><PERSON> <PERSON>ra <PERSON><PERSON> lư<PERSON> records đã backup
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN latitude != 0 AND longitude != 0 THEN 1 END) as records_with_coordinates,
    COUNT(CASE WHEN title IS NOT NULL AND title != '' THEN 1 END) as records_with_title
FROM brand_office_backup_task9;

-- 4. Backup chỉ những records có tọa độ (để so sánh sau này)
CREATE TABLE brand_office_with_coords_backup AS
SELECT id, title, latitude, longitude, updated, updated_by
FROM brand_office 
WHERE latitude != 0 AND longitude != 0 
AND latitude IS NOT NULL AND longitude IS NOT NULL;

-- 5. Tạo log table để track changes
CREATE TABLE brand_office_task9_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    office_id INT NOT NULL,
    old_title VARCHAR(255),
    new_title VARCHAR(255),
    ward_id INT,
    ward_title VARCHAR(255),
    province_title VARCHAR(255),
    latitude DECIMAL(10,5),
    longitude DECIMAL(10,5),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_office_id (office_id)
);

-- 6. Verify backup integrity
SELECT 'Original table count' as description, COUNT(*) as count FROM brand_office
UNION ALL
SELECT 'Backup table count' as description, COUNT(*) as count FROM brand_office_backup_task9
UNION ALL
SELECT 'Coords backup count' as description, COUNT(*) as count FROM brand_office_with_coords_backup;

-- Recovery script (chỉ chạy khi cần restore)
/*
-- CẢNH BÁO: Chỉ chạy khi cần restore dữ liệu gốc

-- Restore từ backup
UPDATE brand_office bo
JOIN brand_office_backup_task9 backup ON bo.id = backup.id
SET 
    bo.title = backup.title,
    bo.updated = backup.updated,
    bo.updated_by = backup.updated_by;

-- Hoặc restore toàn bộ (nguy hiểm)
-- DROP TABLE brand_office;
-- RENAME TABLE brand_office_backup_task9 TO brand_office;
*/
