#!/usr/bin/env python3
"""
Task 9: Update Brand Office Address Using Geometry
Cập nhật cột title trong bảng brand_office dựa trên tọa độ và dữ liệu geometry.
Chỉ giữ lại: s<PERSON>, t<PERSON><PERSON> (khu <PERSON><PERSON>ư), xã/phường, tỉnh/thành phố
Bỏ quận/huyện
"""

import pymysql
import json
import re
from shapely.geometry import Point, Polygon
from shapely.ops import unary_union
import logging
from config import DB_CONFIG

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/task9_brand_office_update.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeAddressUpdater:
    def __init__(self):
        self.db_config = DB_CONFIG
        self.connection = None
        self.ward_geometries = {}
        
    def connect_db(self):
        """Kết nối database"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            logger.info("Kết nối database thành công")
            return True
        except Exception as e:
            logger.error(f"Lỗi kết nối database: {e}")
            return False
    
    def close_db(self):
        """Đóng kết nối database"""
        if self.connection:
            self.connection.close()
            logger.info("Đã đóng kết nối database")
    
    def check_brand_office_schema(self):
        """Kiểm tra schema của bảng brand_office"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("DESCRIBE brand_office")
                schema = cursor.fetchall()
                logger.info("Schema bảng brand_office:")
                for field in schema:
                    logger.info(f"  {field}")
                return schema
        except Exception as e:
            logger.error(f"Lỗi kiểm tra schema brand_office: {e}")
            return None
    
    def check_sample_data(self, limit=5):
        """Xem dữ liệu mẫu từ bảng brand_office"""
        try:
            with self.connection.cursor() as cursor:
                # Kiểm tra dữ liệu có title và tọa độ thật
                cursor.execute(f"""
                    SELECT id, title, address, latitude, longitude
                    FROM brand_office
                    WHERE latitude != 0 AND longitude != 0
                    AND latitude != 1.00000 AND longitude != 10.00000
                    AND (title IS NOT NULL OR address IS NOT NULL)
                    LIMIT {limit}
                """)
                samples = cursor.fetchall()
                logger.info(f"Dữ liệu mẫu từ brand_office có tọa độ thật (top {limit}):")
                for sample in samples:
                    logger.info(f"  ID: {sample[0]}, Title: {sample[1]}, Address: {sample[2]}, Lat: {sample[3]}, Lng: {sample[4]}")

                # Nếu không có dữ liệu thật, xem tất cả dữ liệu
                if not samples:
                    cursor.execute(f"""
                        SELECT id, title, address, latitude, longitude
                        FROM brand_office
                        LIMIT {limit}
                    """)
                    all_samples = cursor.fetchall()
                    logger.info(f"Tất cả dữ liệu mẫu từ brand_office (top {limit}):")
                    for sample in all_samples:
                        logger.info(f"  ID: {sample[0]}, Title: {sample[1]}, Address: {sample[2]}, Lat: {sample[3]}, Lng: {sample[4]}")

                return samples
        except Exception as e:
            logger.error(f"Lỗi lấy dữ liệu mẫu: {e}")
            return None
    
    def load_ward_geometries(self):
        """Load dữ liệu geometry từ bảng geo_ward"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id, ward_title, province_title, geometry
                    FROM geo_ward
                    WHERE geometry IS NOT NULL AND geometry != ''
                """)
                results = cursor.fetchall()
                
                logger.info(f"Đã load {len(results)} ward geometries")
                
                for row in results:
                    ward_id, ward_title, province_title, geometry_json = row
                    try:
                        geometry_data = json.loads(geometry_json)
                        # Tạo Shapely Polygon từ geometry data
                        if 'rings' in geometry_data:
                            # ArcGIS format
                            rings = geometry_data['rings']
                            if rings:
                                polygon = Polygon(rings[0])  # Lấy ring đầu tiên
                                self.ward_geometries[ward_id] = {
                                    'ward_title': ward_title,
                                    'province_title': province_title,
                                    'polygon': polygon
                                }
                    except Exception as e:
                        logger.warning(f"Lỗi parse geometry cho ward_id {ward_id}: {e}")
                        continue
                
                logger.info(f"Đã parse thành công {len(self.ward_geometries)} ward geometries")
                return True
                
        except Exception as e:
            logger.error(f"Lỗi load ward geometries: {e}")
            return False
    
    def find_ward_by_coordinates(self, latitude, longitude):
        """Tìm xã/phường dựa trên tọa độ"""
        point = Point(longitude, latitude)  # Shapely sử dụng (lng, lat)

        for ward_id, ward_data in self.ward_geometries.items():
            try:
                if ward_data['polygon'].contains(point):
                    return {
                        'ward_id': ward_id,
                        'ward_title': ward_data['ward_title'],
                        'province_title': ward_data['province_title']
                    }
            except Exception as e:
                logger.warning(f"Lỗi check point in polygon cho ward_id {ward_id}: {e}")
                continue

        return None
    
    def parse_current_address(self, title):
        """Parse địa chỉ hiện tại để lấy số nhà và tên đường"""
        if not title:
            return None, None
        
        # Tách địa chỉ thành các phần
        # Format thường: "Số nhà, Tên đường, Xã/Phường, Quận/Huyện, Tỉnh/Thành phố"
        parts = [part.strip() for part in title.split(',')]
        
        if len(parts) >= 2:
            # Lấy 2 phần đầu: số nhà và tên đường
            street_number = parts[0]
            street_name = parts[1]
            return street_number, street_name
        elif len(parts) == 1:
            # Chỉ có 1 phần, coi như là tên đường
            return "", parts[0]
        
        return None, None
    
    def format_new_address(self, street_number, street_name, ward_title, province_title):
        """Format địa chỉ mới theo yêu cầu"""
        address_parts = []
        
        if street_number:
            address_parts.append(street_number)
        if street_name:
            address_parts.append(street_name)
        if ward_title:
            address_parts.append(ward_title)
        if province_title:
            address_parts.append(province_title)
        
        return ", ".join(address_parts)
    
    def update_brand_office_addresses(self, dry_run=True):
        """Cập nhật địa chỉ cho brand_office"""
        try:
            with self.connection.cursor() as cursor:
                # Lấy tất cả brand_office có tọa độ
                cursor.execute("""
                    SELECT id, title, latitude, longitude 
                    FROM brand_office 
                    WHERE latitude != 0 AND longitude != 0 
                    AND latitude IS NOT NULL AND longitude IS NOT NULL
                """)
                offices = cursor.fetchall()
                
                logger.info(f"Tìm thấy {len(offices)} brand offices có tọa độ")
                
                updated_count = 0
                not_found_count = 0
                error_count = 0
                
                for office in offices:
                    office_id, current_title, latitude, longitude = office
                    
                    try:
                        # Tìm xã/phường dựa trên tọa độ
                        ward_info = self.find_ward_by_coordinates(float(latitude), float(longitude))
                        
                        if ward_info:
                            # Parse địa chỉ hiện tại
                            street_number, street_name = self.parse_current_address(current_title)
                            
                            # Format địa chỉ mới
                            new_title = self.format_new_address(
                                street_number, 
                                street_name,
                                ward_info['ward_title'],
                                ward_info['province_title']
                            )
                            
                            logger.info(f"Office ID {office_id}:")
                            logger.info(f"  Cũ: {current_title}")
                            logger.info(f"  Mới: {new_title}")
                            logger.info(f"  Ward: {ward_info['ward_title']}, Province: {ward_info['province_title']}")
                            
                            if not dry_run and new_title != current_title:
                                # Cập nhật database
                                update_cursor = self.connection.cursor()
                                update_cursor.execute("""
                                    UPDATE brand_office 
                                    SET title = %s, updated_at = UNIX_TIMESTAMP(), updated_by = 'system:task9'
                                    WHERE id = %s
                                """, (new_title, office_id))
                                update_cursor.close()
                                
                            updated_count += 1
                        else:
                            logger.warning(f"Không tìm thấy ward cho Office ID {office_id} tại ({latitude}, {longitude})")
                            not_found_count += 1
                            
                    except Exception as e:
                        logger.error(f"Lỗi xử lý Office ID {office_id}: {e}")
                        error_count += 1
                
                if not dry_run:
                    self.connection.commit()
                    logger.info("Đã commit các thay đổi vào database")
                
                logger.info(f"Kết quả: Updated: {updated_count}, Not found: {not_found_count}, Errors: {error_count}")
                
        except Exception as e:
            logger.error(f"Lỗi cập nhật brand office addresses: {e}")
            if not dry_run:
                self.connection.rollback()

def main():
    updater = BrandOfficeAddressUpdater()
    
    if not updater.connect_db():
        return
    
    try:
        # Kiểm tra schema
        logger.info("=== KIỂM TRA SCHEMA BRAND_OFFICE ===")
        schema = updater.check_brand_office_schema()
        
        if not schema:
            logger.error("Không thể kiểm tra schema brand_office")
            return
        
        # Xem dữ liệu mẫu
        logger.info("\n=== DỮ LIỆU MẪU ===")
        samples = updater.check_sample_data(10)
        
        # Load ward geometries
        logger.info("\n=== LOAD WARD GEOMETRIES ===")
        if not updater.load_ward_geometries():
            logger.error("Không thể load ward geometries")
            return
        
        # Chạy dry run trước
        logger.info("\n=== DRY RUN - KIỂM TRA TRƯỚC KHI CẬP NHẬT ===")
        updater.update_brand_office_addresses(dry_run=True)
        
        # Hỏi user có muốn thực hiện cập nhật thật không
        response = input("\nBạn có muốn thực hiện cập nhật thật vào database không? (y/N): ")
        if response.lower() == 'y':
            logger.info("\n=== THỰC HIỆN CẬP NHẬT ===")
            updater.update_brand_office_addresses(dry_run=False)
        else:
            logger.info("Hủy cập nhật theo yêu cầu user")
        
    finally:
        updater.close_db()

if __name__ == "__main__":
    main()
