#!/usr/bin/env python3
"""
Geometry Format Checker for Task 9
Kiểm tra format dữ liệu geometry trong bảng geo_ward để điều chỉnh parser
"""

import pymysql
import json
import sys
import os

# Thêm đường dẫn để import config từ crawl_ward
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'crawl_ward'))
from config import DB_CONFIG

def check_geometry_formats():
    """Kiểm tra các format geometry khác nhau trong database"""
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Kết nối database thành công")
        
        with connection.cursor() as cursor:
            # L<PERSON>y một số mẫu geometry
            cursor.execute("""
                SELECT id, ward_title, province_title, geometry 
                FROM geo_ward 
                WHERE geometry IS NOT NULL AND geometry != ''
                LIMIT 5
            """)
            samples = cursor.fetchall()
            
            print(f"\n📊 Tìm thấy {len(samples)} mẫu geometry để kiểm tra:")
            
            formats_found = {
                'arcgis_rings': 0,
                'geojson_polygon': 0,
                'geojson_multipolygon': 0,
                'unknown': 0,
                'invalid_json': 0
            }
            
            for i, (ward_id, ward_title, province_title, geometry_json) in enumerate(samples, 1):
                print(f"\n--- Mẫu {i}: Ward ID {ward_id} - {ward_title}, {province_title} ---")
                
                try:
                    # Parse JSON
                    geometry_data = json.loads(geometry_json)
                    
                    # Hiển thị structure
                    print(f"🔍 Keys trong geometry: {list(geometry_data.keys())}")
                    
                    # Kiểm tra format
                    if 'rings' in geometry_data:
                        formats_found['arcgis_rings'] += 1
                        print("📐 Format: ArcGIS (có 'rings')")
                        rings = geometry_data['rings']
                        print(f"   Số rings: {len(rings)}")
                        if rings:
                            print(f"   Ring đầu tiên có {len(rings[0])} points")
                            print(f"   Point đầu tiên: {rings[0][0] if rings[0] else 'N/A'}")
                    
                    elif 'coordinates' in geometry_data:
                        geom_type = geometry_data.get('type', 'Unknown')
                        print(f"📐 Format: GeoJSON (type: {geom_type})")
                        
                        if geom_type == 'Polygon':
                            formats_found['geojson_polygon'] += 1
                            coords = geometry_data['coordinates']
                            print(f"   Số rings: {len(coords)}")
                            if coords:
                                print(f"   Ring đầu tiên có {len(coords[0])} points")
                                print(f"   Point đầu tiên: {coords[0][0] if coords[0] else 'N/A'}")
                        
                        elif geom_type == 'MultiPolygon':
                            formats_found['geojson_multipolygon'] += 1
                            coords = geometry_data['coordinates']
                            print(f"   Số polygons: {len(coords)}")
                            if coords and coords[0]:
                                print(f"   Polygon đầu có {len(coords[0])} rings")
                                if coords[0][0]:
                                    print(f"   Ring đầu có {len(coords[0][0])} points")
                                    print(f"   Point đầu tiên: {coords[0][0][0] if coords[0][0] else 'N/A'}")
                        else:
                            formats_found['unknown'] += 1
                            print(f"   ⚠️ GeoJSON type không xác định: {geom_type}")
                    
                    else:
                        formats_found['unknown'] += 1
                        print("❓ Format không xác định (không có 'rings' hay 'coordinates')")
                        print(f"   Available keys: {list(geometry_data.keys())}")
                    
                    # Hiển thị một phần dữ liệu
                    geometry_str = str(geometry_data)
                    if len(geometry_str) > 200:
                        geometry_str = geometry_str[:200] + "..."
                    print(f"📄 Dữ liệu (200 ký tự đầu): {geometry_str}")
                    
                except json.JSONDecodeError as e:
                    formats_found['invalid_json'] += 1
                    print(f"❌ Lỗi parse JSON: {e}")
                    print(f"📄 Raw data (100 ký tự đầu): {geometry_json[:100]}...")
                
                except Exception as e:
                    formats_found['unknown'] += 1
                    print(f"❌ Lỗi khác: {e}")
            
            # Tổng kết
            print(f"\n📈 TỔNG KẾT FORMATS:")
            for format_type, count in formats_found.items():
                if count > 0:
                    print(f"   {format_type}: {count} mẫu")
            
            # Đề xuất parser
            print(f"\n💡 ĐỀ XUẤT PARSER:")
            if formats_found['arcgis_rings'] > 0:
                print("   ✅ Cần parser cho ArcGIS format (rings)")
            if formats_found['geojson_polygon'] > 0:
                print("   ✅ Cần parser cho GeoJSON Polygon")
            if formats_found['geojson_multipolygon'] > 0:
                print("   ✅ Cần parser cho GeoJSON MultiPolygon")
            if formats_found['unknown'] > 0:
                print("   ⚠️ Có format không xác định, cần kiểm tra thêm")
            if formats_found['invalid_json'] > 0:
                print("   ❌ Có dữ liệu JSON không hợp lệ")
        
        connection.close()
        print("\n✅ Hoàn thành kiểm tra")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def generate_parser_code(formats_found):
    """Tạo code parser dựa trên formats tìm thấy"""
    
    parser_code = '''
def parse_geometry(geometry_json):
    """Parse geometry từ nhiều formats khác nhau"""
    try:
        geometry_data = json.loads(geometry_json)
        
        # ArcGIS format
        if 'rings' in geometry_data:
            rings = geometry_data['rings']
            if rings:
                return Polygon(rings[0])
        
        # GeoJSON format
        elif 'coordinates' in geometry_data:
            geom_type = geometry_data.get('type', '')
            coordinates = geometry_data['coordinates']
            
            if geom_type == 'Polygon' and coordinates:
                return Polygon(coordinates[0])
            
            elif geom_type == 'MultiPolygon' and coordinates:
                # Lấy polygon đầu tiên
                if coordinates[0] and coordinates[0][0]:
                    return Polygon(coordinates[0][0])
        
        return None
        
    except Exception as e:
        logger.warning(f"Lỗi parse geometry: {e}")
        return None
    '''
    
    print("\n🔧 PARSER CODE ĐỀ XUẤT:")
    print(parser_code)

if __name__ == "__main__":
    check_geometry_formats()
