## task crawl
1 crawl dữ liệu tỉnh thành(tỉnh) mới, lư<PERSON> thông tin vào bảng ___province
2 crawl dữ liệu xã phường(xã) mới, lưu thông tin vào bảng ward
-  map dữ liệu tỉnh và xã mới. 1 tỉnh có nhiều xã. 1 xã chỉ thuộc 1 tỉnh
3 crawl dữ liệu geometry của tỉnh, lưu thông tin vào bảng geo_province
4 crawl dữ liệu geometry của xã, lưu thông tin vào bảng geo_ward.geometry
## Task Database
5 bảng ___province chứa tỉnh cũ và mới, tỉnh mới sẽ bao gồm các tỉnh cũ.Một số tỉnh sẽ không sáp nhập mà giữ nguyên. ví dụ: Ninh Bình mới sẽ được hợp thành từ 3 tỉnh: <PERSON><PERSON>, <PERSON><PERSON>, Nam Định => cần thêm cột thông tin new_province_id để lưu ID của tỉnh mới ở các tỉnh thành cũ, t<PERSON><PERSON> thà<PERSON> mới và cũ được phân biệt bởi cột is_merge = 2( tỉnh mới)
6 bảng ward chứa xã cũ và mới, tương tự như task số 5 tuy nhiên sẽ không có xã nào giữ nguyên mà sẽ được hợp nhất lại thành xã lớn hơn, cần biết xã mới được hợp thành từ các xã cũ nào
7 bảng brand_office, đồng bộ lại dữ liệu lat, long cho chính xác
8 bảng brand_store, đồng bộ lại dữ liệu lat, long cho chính xác
9 bảng brand_office, sau khi đã đồng bộ lại dữ liệu lat,long. dựa vào dữ liệu geometry lấy được ở task số 4, sử dụng thư viện để xem tọa độ của bản ghi thuộc địa phận xã, phường nào, sau đó cập nhật lại vào cột title. Cột title hiện tại là thông tin đầy đủ số nhà, tên dường(khu dân cư), xã/phường, quận/huyện, tỉnh/thành phố. yêu cầu chỉ cập nhật lại xã phường và tỉnh thành, bỏ quận, huyện. giữ nguyên số nhà, tên đường, khu dân cư
10 bảng brand_store, lên task tương tự như task 9
11 bảng gift_receiver: thêm cột để đánh dấu sử dụng dữ liệu mới( hoặc có thể đánh dấu từ ID hoặc created nào sẽ sử dụng dữ liệu mới)
12 bảng address: thêm task tương tự như task 11
13 bảng cart: đang phân tích
14 bảng cart_detail: đang phân tích
## task API
15 viết API lấy danh sách tỉnh mới
16 viết API lấy danh sách xã dựa trên ID tỉnh
17 cập nhật whitelabel
18 cập nhật API phục vụ đối tác(CPV)
19 cập nhật api gọi sang đối tác(NHANH)
20 cập nhật API cho APP
21 cập nhật API giftlink